// 导出服务

import * as XLSX from 'xlsx';
import type { MatchResult, StudentInfo } from '../types';
import { generateFileName } from '../utils/fileUtils';

/**
 * 导出匹配结果为Excel文件
 */
export const exportToExcel = (
  results: MatchResult[],
  studentInfo: StudentInfo,
  fileName?: string
): void => {
  try {
    // 准备数据
    const exportData = results.map(result => ({
      '志愿序号': result.序号,
      '院校代码': result.院校代码,
      '院校名称': result.院校名称,
      '专业代码': result.专业代码,
      '专业名称': result.专业名称,
      '录取位次': result.匹配状态 === 'not_found' ? '未找到' : result.录取位次,
      '录取状态': result.是否录取 ? '可录取' : 
                  result.匹配状态 === 'not_found' ? '未找到' : '录取困难',
      '匹配状态': result.匹配状态 === 'success' ? '匹配成功' : 
                  result.匹配状态 === 'not_found' ? '未找到数据' : '匹配错误'
    }));

    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    
    // 创建工作表
    const worksheet = XLSX.utils.json_to_sheet(exportData);

    // 设置列宽
    const columnWidths = [
      { wch: 10 }, // 志愿序号
      { wch: 12 }, // 院校代码
      { wch: 25 }, // 院校名称
      { wch: 12 }, // 专业代码
      { wch: 25 }, // 专业名称
      { wch: 12 }, // 录取位次
      { wch: 12 }, // 录取状态
      { wch: 12 }  // 匹配状态
    ];
    worksheet['!cols'] = columnWidths;

    // 设置样式（为录取的行添加背景色）
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    
    for (let row = 1; row <= range.e.r; row++) {
      const result = results[row - 1];
      if (result && result.是否录取) {
        // 为录取的行设置蓝色背景
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
          if (!worksheet[cellAddress]) continue;
          
          worksheet[cellAddress].s = {
            fill: {
              fgColor: { rgb: "DBEAFE" } // 蓝色背景
            },
            font: {
              bold: true,
              color: { rgb: "1E40AF" } // 蓝色字体
            }
          };
        }
      }
    }

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '志愿匹配结果');

    // 创建信息工作表
    const infoData = [
      ['学生信息', ''],
      ['考生位次', studentInfo.位次 || ''],
      ['考生分数', studentInfo.分数 || ''],
      ['', ''],
      ['统计信息', ''],
      ['总志愿数', results.length],
      ['可录取数', results.filter(r => r.是否录取).length],
      ['录取困难数', results.filter(r => !r.是否录取 && r.匹配状态 === 'success').length],
      ['未找到数', results.filter(r => r.匹配状态 === 'not_found').length],
      ['录取率', `${((results.filter(r => r.是否录取).length / results.length) * 100).toFixed(1)}%`],
      ['', ''],
      ['导出时间', new Date().toLocaleString('zh-CN')]
    ];

    const infoWorksheet = XLSX.utils.aoa_to_sheet(infoData);
    infoWorksheet['!cols'] = [{ wch: 15 }, { wch: 20 }];
    XLSX.utils.book_append_sheet(workbook, infoWorksheet, '统计信息');

    // 生成文件名
    const finalFileName = fileName || generateFileName('志愿匹配结果', 'xlsx');

    // 下载文件
    XLSX.writeFile(workbook, finalFileName);
  } catch (error) {
    throw new Error(`导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

/**
 * 导出CSV格式
 */
export const exportToCSV = (
  results: MatchResult[],
  studentInfo: StudentInfo,
  fileName?: string
): void => {
  try {
    // 准备CSV数据
    const csvData = results.map(result => ({
      志愿序号: result.序号,
      院校代码: result.院校代码,
      院校名称: result.院校名称,
      专业代码: result.专业代码,
      专业名称: result.专业名称,
      录取位次: result.匹配状态 === 'not_found' ? '未找到' : result.录取位次,
      录取状态: result.是否录取 ? '可录取' : 
                result.匹配状态 === 'not_found' ? '未找到' : '录取困难'
    }));

    // 转换为CSV格式
    const headers = Object.keys(csvData[0]);
    const csvContent = [
      headers.join(','),
      ...csvData.map(row => 
        headers.map(header => `"${row[header as keyof typeof row]}"`).join(',')
      )
    ].join('\n');

    // 添加BOM以支持中文
    const bom = '\uFEFF';
    const blob = new Blob([bom + csvContent], { type: 'text/csv;charset=utf-8;' });

    // 创建下载链接
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', fileName || generateFileName('志愿匹配结果', 'csv'));
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  } catch (error) {
    throw new Error(`CSV导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

/**
 * 导出详细报告（包含建议）
 */
export const exportDetailedReport = (
  results: MatchResult[],
  studentInfo: StudentInfo,
  advice: string[],
  fileName?: string
): void => {
  try {
    const workbook = XLSX.utils.book_new();

    // 主要结果表
    const mainData = results.map(result => ({
      '志愿序号': result.序号,
      '院校代码': result.院校代码,
      '院校名称': result.院校名称,
      '专业代码': result.专业代码,
      '专业名称': result.专业名称,
      '录取位次': result.匹配状态 === 'not_found' ? '未找到' : result.录取位次,
      '学生位次': studentInfo.位次,
      '位次差距': result.匹配状态 === 'not_found' ? '未找到' : 
                  (studentInfo.位次! - result.录取位次),
      '录取状态': result.是否录取 ? '可录取' : 
                  result.匹配状态 === 'not_found' ? '未找到' : '录取困难'
    }));

    const mainWorksheet = XLSX.utils.json_to_sheet(mainData);
    XLSX.utils.book_append_sheet(workbook, mainWorksheet, '详细结果');

    // 建议表
    const adviceData = advice.map((item, index) => ({
      '序号': index + 1,
      '建议内容': item
    }));

    const adviceWorksheet = XLSX.utils.json_to_sheet(adviceData);
    XLSX.utils.book_append_sheet(workbook, adviceWorksheet, '录取建议');

    // 下载
    const finalFileName = fileName || generateFileName('详细报告', 'xlsx');
    XLSX.writeFile(workbook, finalFileName);
  } catch (error) {
    throw new Error(`详细报告导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};
