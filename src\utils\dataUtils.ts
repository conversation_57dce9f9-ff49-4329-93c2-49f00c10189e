// 数据处理工具函数

import type { CSVRecord, VolunteerRecord, MatchResult, StudentInfo } from '../types';
import { DEFAULT_CONFIG } from './constants';

/**
 * 生成索引键
 */
export const generateIndexKey = (schoolCode: string, majorCode: string): string => {
  return `${schoolCode}${DEFAULT_CONFIG.INDEX_KEY_SEPARATOR}${majorCode}`;
};

/**
 * 解析索引键
 */
export const parseIndexKey = (key: string): { schoolCode: string; majorCode: string } => {
  const [schoolCode, majorCode] = key.split(DEFAULT_CONFIG.INDEX_KEY_SEPARATOR);
  return { schoolCode, majorCode };
};

/**
 * 创建CSV数据索引
 */
export const createCSVIndex = (records: CSVRecord[]): Map<string, CSVRecord> => {
  const index = new Map<string, CSVRecord>();
  
  records.forEach(record => {
    const key = generateIndexKey(record.学校代号, record.专业代号);
    index.set(key, record);
  });
  
  return index;
};

/**
 * 判断是否录取
 */
export const isAdmitted = (studentRank: number, admissionRank: number): boolean => {
  return studentRank <= admissionRank;
};

/**
 * 匹配志愿记录
 */
export const matchVolunteerRecord = (
  volunteer: VolunteerRecord,
  csvIndex: Map<string, CSVRecord>,
  studentInfo: StudentInfo
): MatchResult => {
  const key = generateIndexKey(volunteer.院校代码, volunteer.专业代码);
  const csvRecord = csvIndex.get(key);
  
  if (!csvRecord) {
    return {
      ...volunteer,
      录取位次: 0,
      是否录取: false,
      院校名称: volunteer.院校名称 || '未找到',
      专业名称: volunteer.专业名称 || '未找到',
      匹配状态: 'not_found'
    };
  }
  
  const admitted = studentInfo.位次 ? isAdmitted(studentInfo.位次, csvRecord.位次) : false;
  
  return {
    ...volunteer,
    录取位次: csvRecord.位次,
    是否录取: admitted,
    院校名称: csvRecord.学校名称,
    专业名称: csvRecord.专业名称,
    匹配状态: 'success'
  };
};

/**
 * 批量匹配志愿记录
 */
export const batchMatchVolunteers = (
  volunteers: VolunteerRecord[],
  csvIndex: Map<string, CSVRecord>,
  studentInfo: StudentInfo
): MatchResult[] => {
  return volunteers.map(volunteer => 
    matchVolunteerRecord(volunteer, csvIndex, studentInfo)
  );
};

/**
 * 自动检测列名
 */
export const detectColumnName = (headers: string[], variants: string[]): string | null => {
  for (const header of headers) {
    const normalizedHeader = header.trim();
    if (variants.includes(normalizedHeader)) {
      return normalizedHeader;
    }
  }
  return null;
};

/**
 * 验证CSV数据格式
 */
export const validateCSVData = (data: any[]): { valid: boolean; error?: string } => {
  if (!Array.isArray(data) || data.length === 0) {
    return { valid: false, error: 'CSV文件为空或格式错误' };
  }
  
  const firstRow = data[0];
  const requiredColumns = ['学校代号', '学校名称', '专业代号', '专业名称', '位次'];
  
  for (const column of requiredColumns) {
    if (!(column in firstRow)) {
      return { valid: false, error: `缺少必要列: ${column}` };
    }
  }
  
  return { valid: true };
};

/**
 * 清理和标准化数据
 */
export const normalizeCSVRecord = (record: any): CSVRecord => {
  return {
    学校代号: String(record.学校代号 || '').trim(),
    学校名称: String(record.学校名称 || '').trim(),
    专业代号: String(record.专业代号 || '').trim(),
    专业名称: String(record.专业名称 || '').trim(),
    计划数: parseInt(record.计划数) || 0,
    分数线: parseInt(record.分数线) || 0,
    位次: parseInt(record.位次) || 0
  };
};

/**
 * 统计匹配结果
 */
export const getMatchStatistics = (results: MatchResult[]) => {
  const total = results.length;
  const admitted = results.filter(r => r.是否录取).length;
  const notFound = results.filter(r => r.匹配状态 === 'not_found').length;
  const found = total - notFound;
  
  return {
    total,
    admitted,
    notAdmitted: found - admitted,
    notFound,
    admissionRate: total > 0 ? (admitted / total * 100).toFixed(1) : '0'
  };
};
