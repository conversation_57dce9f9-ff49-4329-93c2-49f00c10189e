// 学生信息输入组件

import React, { useState, useCallback } from 'react';
import type { StudentInfo } from '../types';

interface StudentInfoFormProps {
  onSubmit: (studentInfo: StudentInfo) => void;
  disabled?: boolean;
}

const StudentInfoForm: React.FC<StudentInfoFormProps> = ({ onSubmit, disabled = false }) => {
  const [studentInfo, setStudentInfo] = useState<StudentInfo>({});
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // 验证输入
  const validateInput = useCallback((info: StudentInfo): { [key: string]: string } => {
    const newErrors: { [key: string]: string } = {};

    if (!info.位次) {
      newErrors.位次 = '请输入考生位次';
    } else if (info.位次 <= 0) {
      newErrors.位次 = '位次必须大于0';
    } else if (info.位次 > 1000000) {
      newErrors.位次 = '位次不能超过1000000';
    }

    if (info.分数 !== undefined) {
      if (info.分数 < 0) {
        newErrors.分数 = '分数不能为负数';
      } else if (info.分数 > 750) {
        newErrors.分数 = '分数不能超过750';
      }
    }

    return newErrors;
  }, []);

  // 处理位次输入
  const handleRankChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const rank = value ? parseInt(value) : undefined;
    
    const newInfo = { ...studentInfo, 位次: rank };
    setStudentInfo(newInfo);
    
    // 实时验证
    const newErrors = validateInput(newInfo);
    setErrors(newErrors);
  }, [studentInfo, validateInput]);

  // 处理分数输入
  const handleScoreChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const score = value ? parseInt(value) : undefined;
    
    const newInfo = { ...studentInfo, 分数: score };
    setStudentInfo(newInfo);
    
    // 实时验证
    const newErrors = validateInput(newInfo);
    setErrors(newErrors);
  }, [studentInfo, validateInput]);

  // 提交表单
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    
    const validationErrors = validateInput(studentInfo);
    setErrors(validationErrors);
    
    if (Object.keys(validationErrors).length === 0) {
      onSubmit(studentInfo);
    }
  }, [studentInfo, validateInput, onSubmit]);

  // 清空表单
  const handleReset = useCallback(() => {
    setStudentInfo({});
    setErrors({});
  }, []);

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-lg font-medium text-gray-900 mb-6">学生信息</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* 位次输入 */}
        <div>
          <label htmlFor="rank" className="block text-sm font-medium text-gray-700 mb-1">
            考生位次 <span className="text-red-500">*</span>
          </label>
          <input
            type="number"
            id="rank"
            min="1"
            max="1000000"
            placeholder="请输入考生位次"
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-colors ${
              errors.位次 
                ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
            }`}
            value={studentInfo.位次 || ''}
            onChange={handleRankChange}
            disabled={disabled}
          />
          {errors.位次 && (
            <p className="mt-1 text-sm text-red-600">{errors.位次}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            位次是录取判断的主要依据，必须填写
          </p>
        </div>

        {/* 分数输入 */}
        <div>
          <label htmlFor="score" className="block text-sm font-medium text-gray-700 mb-1">
            考生分数（可选）
          </label>
          <input
            type="number"
            id="score"
            min="0"
            max="750"
            placeholder="请输入考生分数"
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-colors ${
              errors.分数 
                ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
            }`}
            value={studentInfo.分数 || ''}
            onChange={handleScoreChange}
            disabled={disabled}
          />
          {errors.分数 && (
            <p className="mt-1 text-sm text-red-600">{errors.分数}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            分数仅用于参考，不影响录取判断
          </p>
        </div>

        {/* 按钮组 */}
        <div className="flex space-x-3 pt-4">
          <button
            type="submit"
            disabled={disabled || !studentInfo.位次 || Object.keys(errors).length > 0}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            确认信息
          </button>
          <button
            type="button"
            onClick={handleReset}
            disabled={disabled}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            重置
          </button>
        </div>
      </form>

      {/* 信息预览 */}
      {studentInfo.位次 && Object.keys(errors).length === 0 && (
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-sm font-medium text-blue-900 mb-2">信息确认</h3>
          <div className="text-sm text-blue-800">
            <p>考生位次: {studentInfo.位次}</p>
            {studentInfo.分数 && <p>考生分数: {studentInfo.分数}</p>}
          </div>
        </div>
      )}
    </div>
  );
};

export default StudentInfoForm;
