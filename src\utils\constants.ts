// 系统常量定义

// 文件配置
export const FILE_CONFIG = {
  MAX_CSV_SIZE: 50 * 1024 * 1024, // 50MB
  MAX_EXCEL_SIZE: 10 * 1024 * 1024, // 10MB
  SUPPORTED_CSV_FORMATS: ['.csv'] as string[],
  SUPPORTED_EXCEL_FORMATS: ['.xlsx', '.xls'] as string[],
  CSV_ENCODING: 'UTF-8'
};

// CSV列名映射
export const CSV_COLUMNS = {
  SCHOOL_CODE: '学校代号',
  SCHOOL_NAME: '学校名称',
  MAJOR_CODE: '专业代号',
  MAJOR_NAME: '专业名称',
  PLAN_COUNT: '计划数',
  SCORE_LINE: '分数线',
  RANK: '位次'
} as const;

// Excel详细格式列名
export const DETAILED_EXCEL_COLUMNS = {
  ORDER: '志愿序号',
  SCHOOL_NAME: '院校名称',
  SCHOOL_CODE: '院校代码',
  MAJOR_NAME: '专业名称',
  MAJOR_CODE: '专业代码'
} as const;

// 可能的列名变体（用于自动识别）
export const COLUMN_VARIANTS = {
  SCHOOL_CODE: ['院校代码', '学校代号', '院校代号', '学校编码'] as string[],
  SCHOOL_NAME: ['院校名称', '学校名称', '院校', '学校'] as string[],
  MAJOR_CODE: ['专业代码', '专业代号', '专业编码'] as string[],
  MAJOR_NAME: ['专业名称', '专业'] as string[],
  ORDER: ['志愿序号', '序号', '排序', '顺序'] as string[],
  RANK: ['位次', '排名', '名次'] as string[]
};

// 系统消息
export const MESSAGES = {
  FILE_UPLOAD: {
    CSV_SUCCESS: 'CSV文件上传成功',
    EXCEL_SUCCESS: 'Excel文件上传成功',
    FILE_TOO_LARGE: '文件大小超出限制',
    INVALID_FORMAT: '不支持的文件格式',
    PARSE_ERROR: '文件解析失败'
  },
  MATCHING: {
    SUCCESS: '匹配完成',
    NO_DATA: '未找到匹配数据',
    PROCESSING: '正在处理...'
  },
  EXPORT: {
    SUCCESS: '导出成功',
    ERROR: '导出失败'
  }
} as const;

// 样式配置
export const STYLES = {
  ADMITTED_ROW: 'bg-blue-50 font-bold text-blue-800',
  NOT_ADMITTED_ROW: 'bg-gray-50',
  ERROR_ROW: 'bg-red-50 text-red-600',
  SUCCESS_BADGE: 'bg-green-100 text-green-800',
  ERROR_BADGE: 'bg-red-100 text-red-800'
} as const;

// 默认配置
export const DEFAULT_CONFIG = {
  INDEX_KEY_SEPARATOR: '-',
  BATCH_SIZE: 1000, // 批处理大小
  DEBOUNCE_DELAY: 300 // 防抖延迟（毫秒）
} as const;
