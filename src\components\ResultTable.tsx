// 结果展示表格组件

import React, { useState, useMemo } from 'react';
import type { MatchResult, StudentInfo } from '../types';
import { getMatchStatistics, getAdmissionAdvice } from '../services/matchService';

interface ResultTableProps {
  results: MatchResult[];
  studentInfo: StudentInfo;
  onExport?: () => void;
}

const ResultTable: React.FC<ResultTableProps> = ({ results, studentInfo, onExport }) => {
  const [sortField, setSortField] = useState<keyof MatchResult>('序号');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [filterStatus, setFilterStatus] = useState<'all' | 'admitted' | 'not_admitted' | 'not_found'>('all');

  // 计算统计信息
  const statistics = useMemo(() => getMatchStatistics(results), [results]);
  
  // 获取录取建议
  const advice = useMemo(() => getAdmissionAdvice(results), [results]);

  // 排序和筛选结果
  const filteredAndSortedResults = useMemo(() => {
    let filtered = results;

    // 筛选
    if (filterStatus !== 'all') {
      filtered = results.filter(result => {
        switch (filterStatus) {
          case 'admitted':
            return result.是否录取;
          case 'not_admitted':
            return !result.是否录取 && result.匹配状态 === 'success';
          case 'not_found':
            return result.匹配状态 === 'not_found';
          default:
            return true;
        }
      });
    }

    // 排序
    return filtered.sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }
      
      const aStr = String(aValue);
      const bStr = String(bValue);
      return sortDirection === 'asc' 
        ? aStr.localeCompare(bStr) 
        : bStr.localeCompare(aStr);
    });
  }, [results, sortField, sortDirection, filterStatus]);

  // 处理排序
  const handleSort = (field: keyof MatchResult) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // 获取行样式
  const getRowClassName = (result: MatchResult) => {
    if (result.是否录取) {
      return 'bg-blue-50 border-blue-200';
    }
    if (result.匹配状态 === 'not_found') {
      return 'bg-red-50 border-red-200';
    }
    return 'bg-white border-gray-200';
  };

  // 获取状态标识
  const getStatusBadge = (result: MatchResult) => {
    if (result.是否录取) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          可录取
        </span>
      );
    }
    if (result.匹配状态 === 'not_found') {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          未找到
        </span>
      );
    }
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
        录取困难
      </span>
    );
  };

  if (results.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center py-12">
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">暂无匹配结果</h3>
          <p className="mt-1 text-sm text-gray-500">请先上传CSV数据和Excel志愿表，并填写学生信息</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      {/* 头部统计信息 */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-lg font-medium text-gray-900">匹配结果</h2>
            <p className="mt-1 text-sm text-gray-500">
              学生位次: {studentInfo.位次} {studentInfo.分数 && `| 分数: ${studentInfo.分数}`}
            </p>
          </div>
          {onExport && (
            <button
              onClick={onExport}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors"
            >
              导出Excel
            </button>
          )}
        </div>

        {/* 统计卡片 */}
        <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{statistics.admitted}</div>
            <div className="text-sm text-blue-800">可录取</div>
          </div>
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-2xl font-bold text-gray-600">{statistics.notAdmitted}</div>
            <div className="text-sm text-gray-800">录取困难</div>
          </div>
          <div className="bg-red-50 p-3 rounded-lg">
            <div className="text-2xl font-bold text-red-600">{statistics.notFound}</div>
            <div className="text-sm text-red-800">未找到</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{statistics.admissionRate}%</div>
            <div className="text-sm text-green-800">录取率</div>
          </div>
        </div>

        {/* 录取建议 */}
        {advice.length > 0 && (
          <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
            <h3 className="text-sm font-medium text-yellow-800 mb-2">录取建议</h3>
            <ul className="text-sm text-yellow-700 space-y-1">
              {advice.map((item, index) => (
                <li key={index}>• {item}</li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* 筛选和排序控制 */}
      <div className="px-6 py-3 border-b border-gray-200 bg-gray-50">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">筛选:</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">全部 ({results.length})</option>
              <option value="admitted">可录取 ({statistics.admitted})</option>
              <option value="not_admitted">录取困难 ({statistics.notAdmitted})</option>
              <option value="not_found">未找到 ({statistics.notFound})</option>
            </select>
          </div>
          <div className="text-sm text-gray-500">
            显示 {filteredAndSortedResults.length} 条结果
          </div>
        </div>
      </div>

      {/* 表格 */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {[
                { key: '序号', label: '序号' },
                { key: '院校代码', label: '院校代码' },
                { key: '院校名称', label: '院校名称' },
                { key: '专业代码', label: '专业代码' },
                { key: '专业名称', label: '专业名称' },
                { key: '录取位次', label: '录取位次' },
                { key: '是否录取', label: '录取状态' }
              ].map(({ key, label }) => (
                <th
                  key={key}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => handleSort(key as keyof MatchResult)}
                >
                  <div className="flex items-center space-x-1">
                    <span>{label}</span>
                    {sortField === key && (
                      <svg
                        className={`w-4 h-4 ${sortDirection === 'asc' ? 'transform rotate-180' : ''}`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredAndSortedResults.map((result, index) => (
              <tr
                key={`${result.院校代码}-${result.专业代码}-${index}`}
                className={`${getRowClassName(result)} ${result.是否录取 ? 'font-semibold' : ''}`}
              >
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {result.序号}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {result.院校代码}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {result.院校名称}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {result.专业代码}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {result.专业名称}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {result.匹配状态 === 'not_found' ? '-' : result.录取位次.toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(result)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 空状态 */}
      {filteredAndSortedResults.length === 0 && (
        <div className="text-center py-12">
          <p className="text-sm text-gray-500">没有符合筛选条件的结果</p>
        </div>
      )}
    </div>
  );
};

export default ResultTable;
