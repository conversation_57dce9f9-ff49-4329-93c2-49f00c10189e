// 志愿匹配服务

import type { CSVRecord, VolunteerRecord, MatchResult, StudentInfo } from '../types';
import { batchMatchVolunteers, getMatchStatistics } from '../utils/dataUtils';

/**
 * 执行志愿匹配
 */
export const performMatching = (
  volunteers: VolunteerRecord[],
  csvIndex: Map<string, CSVRecord>,
  studentInfo: StudentInfo
): Promise<{
  results: MatchResult[];
  statistics: ReturnType<typeof getMatchStatistics>;
}> => {
  return new Promise((resolve, reject) => {
    try {
      // 验证学生信息
      if (!studentInfo.位次) {
        reject(new Error('请输入学生位次'));
        return;
      }

      // 执行批量匹配
      const results = batchMatchVolunteers(volunteers, csvIndex, studentInfo);
      
      // 计算统计信息
      const statistics = getMatchStatistics(results);

      resolve({
        results,
        statistics
      });
    } catch (error) {
      reject(new Error(`匹配失败: ${error instanceof Error ? error.message : '未知错误'}`));
    }
  });
};

/**
 * 异步批量匹配（用于大量数据）
 */
export const performAsyncMatching = (
  volunteers: VolunteerRecord[],
  csvIndex: Map<string, CSVRecord>,
  studentInfo: StudentInfo,
  onProgress?: (progress: number) => void
): Promise<{
  results: MatchResult[];
  statistics: ReturnType<typeof getMatchStatistics>;
}> => {
  return new Promise((resolve, reject) => {
    try {
      if (!studentInfo.位次) {
        reject(new Error('请输入学生位次'));
        return;
      }

      const batchSize = 10; // 每批处理10个志愿
      const results: MatchResult[] = [];
      let currentIndex = 0;

      const processBatch = () => {
        const batch = volunteers.slice(currentIndex, currentIndex + batchSize);
        const batchResults = batchMatchVolunteers(batch, csvIndex, studentInfo);
        results.push(...batchResults);

        currentIndex += batchSize;
        const progress = Math.min((currentIndex / volunteers.length) * 100, 100);
        
        if (onProgress) {
          onProgress(progress);
        }

        if (currentIndex < volunteers.length) {
          // 使用setTimeout实现异步处理，避免阻塞UI
          setTimeout(processBatch, 0);
        } else {
          // 处理完成
          const statistics = getMatchStatistics(results);
          resolve({
            results,
            statistics
          });
        }
      };

      // 开始处理
      processBatch();
    } catch (error) {
      reject(new Error(`匹配失败: ${error instanceof Error ? error.message : '未知错误'}`));
    }
  });
};

/**
 * 获取录取建议
 */
export const getAdmissionAdvice = (results: MatchResult[]) => {
  const admitted = results.filter(r => r.是否录取);
  const notAdmitted = results.filter(r => !r.是否录取 && r.匹配状态 === 'success');
  const notFound = results.filter(r => r.匹配状态 === 'not_found');

  const advice = [];

  if (admitted.length > 0) {
    advice.push(`有 ${admitted.length} 个志愿可能被录取`);
    
    // 找到最高位次的录取志愿
    const bestAdmission = admitted.reduce((best, current) => 
      current.序号 < best.序号 ? current : best
    );
    advice.push(`最有希望的志愿是第 ${bestAdmission.序号} 志愿：${bestAdmission.院校名称} - ${bestAdmission.专业名称}`);
  }

  if (notAdmitted.length > 0) {
    advice.push(`有 ${notAdmitted.length} 个志愿录取希望较小`);
  }

  if (notFound.length > 0) {
    advice.push(`有 ${notFound.length} 个志愿在数据库中未找到匹配信息`);
  }

  if (admitted.length === 0 && notAdmitted.length > 0) {
    advice.push('建议调整志愿选择，考虑位次要求更低的专业');
  }

  return advice;
};
