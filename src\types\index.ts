// 高考志愿查询系统类型定义

// CSV数据记录类型
export interface CSVRecord {
  学校代号: string;
  学校名称: string;
  专业代号: string;
  专业名称: string;
  计划数: number;
  分数线: number;
  位次: number;
}

// 志愿记录类型（Excel中的数据）
export interface VolunteerRecord {
  序号: number;
  院校代码: string;
  院校名称?: string;
  专业代码: string;
  专业名称?: string;
}

// 匹配结果类型
export interface MatchResult extends VolunteerRecord {
  录取位次: number;
  是否录取: boolean;
  院校名称: string;
  专业名称: string;
  匹配状态: 'success' | 'not_found' | 'error';
}

// 学生信息类型
export interface StudentInfo {
  位次?: number;
  分数?: number;
}

// 文件上传状态类型
export interface FileUploadStatus {
  status: 'idle' | 'uploading' | 'success' | 'error';
  progress: number;
  message?: string;
}

// Excel文件格式类型
export type ExcelFormat = 'detailed' | 'simple';

// 详细格式的Excel列映射
export interface DetailedExcelColumns {
  序号: string;
  院校名称: string;
  院校代码: string;
  专业名称: string;
  专业代码: string;
}

// 简单格式的Excel列映射
export interface SimpleExcelColumns {
  院校代码: string;
  专业代码: string;
}

// 数据处理配置
export interface DataProcessConfig {
  csvIndexKey: string; // CSV索引键格式，如 "院校代码-专业代码"
  maxFileSize: number; // 最大文件大小（字节）
  supportedFormats: string[]; // 支持的文件格式
}

// 导出配置
export interface ExportConfig {
  fileName: string;
  includeHeaders: boolean;
  highlightAdmitted: boolean;
}
