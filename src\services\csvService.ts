// CSV数据处理服务

import Papa from 'papaparse';
import type { CSVRecord } from '../types';
import { validateCSVData, normalizeCSVRecord, createCSVIndex } from '../utils/dataUtils';
import { CSV_COLUMNS } from '../utils/constants';

/**
 * 解析CSV文件
 */
export const parseCSVFile = (file: File): Promise<{
  data: CSVRecord[];
  index: Map<string, CSVRecord>;
}> => {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      header: true,
      encoding: 'UTF-8',
      skipEmptyLines: true,
      complete: (results) => {
        try {
          // 验证数据格式
          const validation = validateCSVData(results.data);
          if (!validation.valid) {
            reject(new Error(validation.error));
            return;
          }

          // 标准化数据
          const normalizedData = results.data.map(normalizeCSVRecord);
          
          // 创建索引
          const index = createCSVIndex(normalizedData);

          resolve({
            data: normalizedData,
            index
          });
        } catch (error) {
          reject(new Error(`CSV解析失败: ${error instanceof Error ? error.message : '未知错误'}`));
        }
      },
      error: (error) => {
        reject(new Error(`CSV文件读取失败: ${error.message}`));
      }
    });
  });
};

/**
 * 验证CSV文件头
 */
export const validateCSVHeaders = (headers: string[]): { valid: boolean; missing: string[] } => {
  const requiredHeaders = Object.values(CSV_COLUMNS);
  const missing = requiredHeaders.filter(header => !headers.includes(header));
  
  return {
    valid: missing.length === 0,
    missing
  };
};

/**
 * 获取CSV统计信息
 */
export const getCSVStatistics = (data: CSVRecord[]) => {
  const totalRecords = data.length;
  const uniqueSchools = new Set(data.map(record => record.学校代号)).size;
  const uniqueMajors = new Set(data.map(record => `${record.学校代号}-${record.专业代号}`)).size;
  
  const scoreRange = data.reduce(
    (range, record) => ({
      min: Math.min(range.min, record.分数线),
      max: Math.max(range.max, record.分数线)
    }),
    { min: Infinity, max: -Infinity }
  );

  const rankRange = data.reduce(
    (range, record) => ({
      min: Math.min(range.min, record.位次),
      max: Math.max(range.max, record.位次)
    }),
    { min: Infinity, max: -Infinity }
  );

  return {
    totalRecords,
    uniqueSchools,
    uniqueMajors,
    scoreRange: scoreRange.min === Infinity ? { min: 0, max: 0 } : scoreRange,
    rankRange: rankRange.min === Infinity ? { min: 0, max: 0 } : rankRange
  };
};
