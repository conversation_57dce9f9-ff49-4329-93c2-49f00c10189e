// 文件处理工具函数

import { FILE_CONFIG } from './constants';

/**
 * 验证文件大小
 */
export const validateFileSize = (file: File, maxSize: number): boolean => {
  return file.size <= maxSize;
};

/**
 * 验证文件格式
 */
export const validateFileFormat = (file: File, allowedFormats: string[]): boolean => {
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
  return allowedFormats.includes(fileExtension);
};

/**
 * 验证CSV文件
 */
export const validateCSVFile = (file: File): { valid: boolean; error?: string } => {
  if (!validateFileSize(file, FILE_CONFIG.MAX_CSV_SIZE)) {
    return { valid: false, error: `文件大小不能超过 ${FILE_CONFIG.MAX_CSV_SIZE / 1024 / 1024}MB` };
  }
  
  if (!validateFileFormat(file, FILE_CONFIG.SUPPORTED_CSV_FORMATS)) {
    return { valid: false, error: '只支持 .csv 格式文件' };
  }
  
  return { valid: true };
};

/**
 * 验证Excel文件
 */
export const validateExcelFile = (file: File): { valid: boolean; error?: string } => {
  if (!validateFileSize(file, FILE_CONFIG.MAX_EXCEL_SIZE)) {
    return { valid: false, error: `文件大小不能超过 ${FILE_CONFIG.MAX_EXCEL_SIZE / 1024 / 1024}MB` };
  }
  
  if (!validateFileFormat(file, FILE_CONFIG.SUPPORTED_EXCEL_FORMATS)) {
    return { valid: false, error: '只支持 .xlsx 和 .xls 格式文件' };
  }
  
  return { valid: true };
};

/**
 * 读取文件为文本
 */
export const readFileAsText = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result;
      if (typeof result === 'string') {
        resolve(result);
      } else {
        reject(new Error('文件读取失败'));
      }
    };
    reader.onerror = () => reject(new Error('文件读取错误'));
    reader.readAsText(file, FILE_CONFIG.CSV_ENCODING);
  });
};

/**
 * 读取文件为ArrayBuffer
 */
export const readFileAsArrayBuffer = (file: File): Promise<ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result;
      if (result instanceof ArrayBuffer) {
        resolve(result);
      } else {
        reject(new Error('文件读取失败'));
      }
    };
    reader.onerror = () => reject(new Error('文件读取错误'));
    reader.readAsArrayBuffer(file);
  });
};

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 生成下载文件名
 */
export const generateFileName = (prefix: string, extension: string): string => {
  const now = new Date();
  const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '');
  return `${prefix}_${timestamp}.${extension}`;
};
