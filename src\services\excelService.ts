// Excel文件处理服务

import * as XLSX from 'xlsx';
import type { VolunteerRecord, ExcelFormat } from '../types';
import { COLUMN_VARIANTS } from '../utils/constants';
import { detectColumnName } from '../utils/dataUtils';

/**
 * 解析Excel文件
 */
export const parseExcelFile = (file: File): Promise<{
  data: VolunteerRecord[];
  format: ExcelFormat;
}> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (e) => {
      try {
        const data = e.target?.result;
        if (!data) {
          reject(new Error('文件读取失败'));
          return;
        }

        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        
        // 转换为JSON格式
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        if (jsonData.length < 2) {
          reject(new Error('Excel文件数据不足'));
          return;
        }

        // 获取表头
        const headers = jsonData[0] as string[];
        const rows = jsonData.slice(1) as any[][];

        // 检测Excel格式
        const format = detectExcelFormat(headers);
        
        // 解析数据
        const volunteers = parseExcelData(headers, rows, format);
        
        resolve({
          data: volunteers,
          format
        });
      } catch (error) {
        reject(new Error(`Excel解析失败: ${error instanceof Error ? error.message : '未知错误'}`));
      }
    };

    reader.onerror = () => {
      reject(new Error('文件读取错误'));
    };

    reader.readAsArrayBuffer(file);
  });
};

/**
 * 检测Excel格式类型
 */
export const detectExcelFormat = (headers: string[]): ExcelFormat => {
  // 检查是否包含详细格式的列
  const hasSchoolName = detectColumnName(headers, COLUMN_VARIANTS.SCHOOL_NAME) !== null;
  const hasMajorName = detectColumnName(headers, COLUMN_VARIANTS.MAJOR_NAME) !== null;
  const hasOrder = detectColumnName(headers, COLUMN_VARIANTS.ORDER) !== null;
  
  if (hasSchoolName && hasMajorName && hasOrder) {
    return 'detailed';
  }
  
  return 'simple';
};

/**
 * 解析Excel数据
 */
const parseExcelData = (headers: string[], rows: any[][], format: ExcelFormat): VolunteerRecord[] => {
  if (format === 'detailed') {
    return parseDetailedFormat(headers, rows);
  } else {
    return parseSimpleFormat(headers, rows);
  }
};

/**
 * 解析详细格式Excel
 */
const parseDetailedFormat = (headers: string[], rows: any[][]): VolunteerRecord[] => {
  // 检测列位置
  const orderCol = detectColumnName(headers, COLUMN_VARIANTS.ORDER);
  const schoolNameCol = detectColumnName(headers, COLUMN_VARIANTS.SCHOOL_NAME);
  const schoolCodeCol = detectColumnName(headers, COLUMN_VARIANTS.SCHOOL_CODE);
  const majorNameCol = detectColumnName(headers, COLUMN_VARIANTS.MAJOR_NAME);
  const majorCodeCol = detectColumnName(headers, COLUMN_VARIANTS.MAJOR_CODE);

  if (!schoolCodeCol || !majorCodeCol) {
    throw new Error('Excel文件缺少必要的院校代码或专业代码列');
  }

  const orderIndex = headers.indexOf(orderCol || '');
  const schoolNameIndex = headers.indexOf(schoolNameCol || '');
  const schoolCodeIndex = headers.indexOf(schoolCodeCol);
  const majorNameIndex = headers.indexOf(majorNameCol || '');
  const majorCodeIndex = headers.indexOf(majorCodeCol);

  return rows.map((row, index) => ({
    序号: orderIndex >= 0 ? parseInt(row[orderIndex]) || (index + 1) : (index + 1),
    院校代码: String(row[schoolCodeIndex] || '').trim(),
    院校名称: schoolNameIndex >= 0 ? String(row[schoolNameIndex] || '').trim() : undefined,
    专业代码: String(row[majorCodeIndex] || '').trim(),
    专业名称: majorNameIndex >= 0 ? String(row[majorNameIndex] || '').trim() : undefined
  })).filter(record => record.院校代码 && record.专业代码);
};

/**
 * 解析简单格式Excel（考试院格式）
 */
const parseSimpleFormat = (headers: string[], rows: any[][]): VolunteerRecord[] => {
  // 简单格式假设前两列分别是院校代码和专业代码
  if (headers.length < 2) {
    throw new Error('Excel文件格式不正确，至少需要两列数据');
  }

  return rows.map((row, index) => ({
    序号: index + 1,
    院校代码: String(row[0] || '').trim(),
    专业代码: String(row[1] || '').trim()
  })).filter(record => record.院校代码 && record.专业代码);
};

/**
 * 验证志愿数据
 */
export const validateVolunteerData = (data: VolunteerRecord[]): { valid: boolean; error?: string } => {
  if (!Array.isArray(data) || data.length === 0) {
    return { valid: false, error: '志愿数据为空' };
  }

  if (data.length > 80) {
    return { valid: false, error: '志愿数量不能超过80个' };
  }

  const invalidRecords = data.filter(record => !record.院校代码 || !record.专业代码);
  if (invalidRecords.length > 0) {
    return { valid: false, error: '存在缺少院校代码或专业代码的志愿记录' };
  }

  return { valid: true };
};
