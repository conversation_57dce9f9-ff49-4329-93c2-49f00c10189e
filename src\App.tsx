import { useState, useCallback } from 'react';
import type { StudentInfo, MatchResult, CSVRecord, VolunteerRecord, ExcelFormat } from './types';
import { LoadingSpinner, ErrorMessage } from './components/common';
import FileUploader from './components/FileUploader';
import StudentInfoForm from './components/StudentInfoForm';
import ResultTable from './components/ResultTable';
import { performMatching, getAdmissionAdvice } from './services/matchService';
import { exportToExcel } from './services/exportService';

function App() {
  // 状态管理
  const [csvData, setCsvData] = useState<CSVRecord[]>([]);
  const [csvIndex, setCsvIndex] = useState<Map<string, CSVRecord>>(new Map());
  const [volunteerData, setVolunteerData] = useState<VolunteerRecord[]>([]);
  const [matchResults, setMatchResults] = useState<MatchResult[]>([]);
  const [studentInfo, setStudentInfo] = useState<StudentInfo>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const [excelFormat, setExcelFormat] = useState<ExcelFormat>('simple');

  // 处理CSV上传
  const handleCsvUpload = useCallback((data: CSVRecord[], index: Map<string, CSVRecord>) => {
    setCsvData(data);
    setCsvIndex(index);
    setError('');
  }, []);

  // 处理Excel上传
  const handleExcelUpload = useCallback((data: VolunteerRecord[], format: ExcelFormat) => {
    setVolunteerData(data);
    setExcelFormat(format);
    setError('');
  }, []);

  // 处理学生信息提交
  const handleStudentInfoSubmit = useCallback(async (info: StudentInfo) => {
    if (csvData.length === 0) {
      setError('请先上传CSV数据文件');
      return;
    }
    if (volunteerData.length === 0) {
      setError('请先上传Excel志愿表');
      return;
    }

    setStudentInfo(info);
    setLoading(true);
    setError('');

    try {
      const { results } = await performMatching(volunteerData, csvIndex, info);
      setMatchResults(results);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '匹配失败';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [csvData, volunteerData, csvIndex]);

  // 处理导出
  const handleExport = useCallback(() => {
    if (matchResults.length === 0) {
      setError('没有可导出的结果');
      return;
    }

    try {
      exportToExcel(matchResults, studentInfo);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '导出失败';
      setError(errorMessage);
    }
  }, [matchResults, studentInfo]);

  // 处理错误
  const handleError = useCallback((errorMessage: string) => {
    setError(errorMessage);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                高考志愿录取查询系统
              </h1>
              <p className="mt-1 text-sm text-gray-500">
                上传CSV数据和志愿表，快速查询录取可能性
              </p>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {error && (
          <ErrorMessage
            message={error}
            onRetry={() => setError('')}
            className="mb-6"
          />
        )}

        {loading && (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" text="正在处理数据..." />
          </div>
        )}

        {/* 主要功能区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 文件上传区域 */}
          <FileUploader
            onCsvUpload={handleCsvUpload}
            onExcelUpload={handleExcelUpload}
            onError={handleError}
          />

          {/* 学生信息输入 */}
          <StudentInfoForm
            onSubmit={handleStudentInfoSubmit}
            disabled={loading}
          />
        </div>

        {/* 结果展示区域 */}
        {matchResults.length > 0 && (
          <div className="mt-8">
            <ResultTable
              results={matchResults}
              studentInfo={studentInfo}
              onExport={handleExport}
            />
          </div>
        )}

        {/* 数据状态提示 */}
        {csvData.length === 0 && volunteerData.length === 0 && (
          <div className="mt-8 text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">开始使用</h3>
            <p className="mt-1 text-sm text-gray-500">
              请先上传CSV数据文件和Excel志愿表，然后填写学生信息进行匹配
            </p>
          </div>
        )}

        {csvData.length > 0 && volunteerData.length > 0 && matchResults.length === 0 && !loading && (
          <div className="mt-8 text-center py-12">
            <svg className="mx-auto h-12 w-12 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">数据已准备就绪</h3>
            <p className="mt-1 text-sm text-gray-500">
              已上传 {csvData.length} 条录取数据和 {volunteerData.length} 个志愿，请填写学生信息开始匹配
            </p>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;
