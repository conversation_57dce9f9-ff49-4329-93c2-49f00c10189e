import { useState } from 'react'
import './App.css'
import FileUploader from './components/FileUploader'
import StudentInfoForm from './components/StudentInfoForm'
import ResultTable from './components/ResultTable'

interface StudentInfo {
  rank: number
  score?: number
}

interface VolunteerData {
  序号: number
  院校代码: string
  院校名称: string
  专业代码: string
  专业名称: string
  录取位次: number
  是否录取: boolean
}

function App() {
  const [csvData, setCsvData] = useState<any[]>([])
  const [studentInfo, setStudentInfo] = useState<StudentInfo | null>(null)
  const [volunteerList, setVolunteerList] = useState<any[]>([])
  const [results, setResults] = useState<VolunteerData[]>([])

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
          高考志愿录取查询系统
        </h1>
        
        <div className="space-y-6">
          <FileUploader 
            onCsvUpload={setCsvData}
            onExcelUpload={setVolunteerList}
          />
          
          <StudentInfoForm 
            onSubmit={setStudentInfo}
          />
          
          {results.length > 0 && (
            <ResultTable 
              data={results}
              studentInfo={studentInfo}
            />
          )}
        </div>
      </div>
    </div>
  )
}

export default App

